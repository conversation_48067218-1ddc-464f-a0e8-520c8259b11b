# Dentist Salary Feature

## Overview

The Dentist Salary feature allows dentists and administrators to view salary calculations based on procedures performed within a specified date range. The feature calculates salary based on the dentist's percentage and the total revenue from procedures.

## Features

- **Date Range Filtering**: Select custom date ranges to view salary data
- **Dentist Selection**: Admin/Master users can view salary data for any dentist
- **Summary Cards**: Display total revenue, percentage, and calculated salary
- **Procedures List**: Detailed list of all procedures with patient information
- **Real-time Calculations**: Automatic calculation of net amounts (price - discount)
- **Permission-based Access**: Role-based access control for viewing salary data

## Access Control

- **Dentists**: Can view their own salary data
- **Admin/Master**: Can view salary data for any dentist in the clinic
- **Other roles**: No access to salary feature

## How to Use

### For Dentists

1. Click on the "Salary" button in the sidebar navigation
2. The dialog will open with your user pre-selected
3. Choose the date range using the date pickers (defaults to current month)
4. Click "Apply" to fetch salary data
5. View the summary cards showing:
   - Total Revenue: Sum of all procedure prices
   - Percentage: Your salary percentage
   - Salary: Calculated salary (Total × Percentage ÷ 100)
6. Review the procedures list for detailed breakdown

### For Admin/Master Users

1. Click on the "Salary" button in the sidebar navigation
2. Select a dentist from the dropdown
3. Choose the date range using the date pickers
4. Click "Apply" to fetch salary data
5. View the summary and procedures list

## Technical Implementation

### Models

- **DentistSalaryResponse**: Main response model containing totals and procedures
- **SalaryProcedure**: Individual procedure data with patient information
- **PatientLite**: Lightweight patient model for procedure display

### API Endpoint

- **GET** `/users/:uid/salary?from=YYYY-MM-DD&to=YYYY-MM-DD`
- Requires authentication token
- Returns salary data for the specified user and date range

### State Management

- Uses GetX for reactive state management
- States: Initial, Loading, Loaded, Error
- Automatic refresh when parameters change

### UI Components

- **BasicDialog**: Reusable dialog component
- **Date Pickers**: Standard Flutter date selection
- **Summary Cards**: Color-coded cards for key metrics
- **Procedures Table**: Responsive table with patient and procedure details

## Error Handling

- **Permission Errors**: Clear messages for unauthorized access
- **Validation Errors**: Date range validation (from ≤ to)
- **Network Errors**: Retry functionality with error display
- **Empty States**: Friendly messages when no data is available

## Testing

The feature includes comprehensive unit tests covering:

- Model serialization/deserialization
- Net amount calculations
- Edge cases (empty procedures, zero amounts)
- JSON parsing validation

Run tests with:
```bash
flutter test test/salary_feature_test.dart
```

## File Structure

```
lib/
├── core/
│   ├── api/
│   │   └── users.dart (added getDentistSalary method)
│   └── models/
│       ├── dentist_salary.dart
│       ├── salary_procedure.dart
│       └── patient_lite.dart
├── pages/
│   └── salary/
│       ├── controller.dart
│       └── salary_dialog.dart
└── layout/
    └── components/
        └── side_nav.dart (added salary button)

test/
└── salary_feature_test.dart
```

## Future Enhancements

- Export salary data to PDF/Excel
- Historical salary comparisons
- Salary trends and analytics
- Email salary reports
- Bulk salary calculations for multiple dentists
- Integration with payroll systems

## Dependencies

- GetX: State management
- Jiffy: Date formatting
- JSON Annotation: Model serialization
- Flutter Material: UI components

## Notes

- The feature follows existing codebase patterns and conventions
- All text is hardcoded in English (can be internationalized later)
- Currency formatting uses the clinic's configured currency
- Date format follows YYYY-MM-DD pattern for API consistency
