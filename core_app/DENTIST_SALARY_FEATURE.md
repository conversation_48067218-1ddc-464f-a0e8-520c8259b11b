# Dentist Salary Feature

## Overview

The Dentist Salary feature allows dentists and administrators to view salary calculations based on procedures performed within a specified date range. The feature calculates salary based on the dentist's percentage and the total revenue from procedures. All calculations are performed on the frontend using the user's percentage from their profile.

## Features

- **Date Range Filtering**: Select custom date ranges to view salary data
- **User-Specific Access**: Click on a dentist in the Users list to view their salary
- **Summary Cards**: Display total revenue, percentage, and calculated salary
- **Procedures List**: Detailed list of all procedures with patient information
- **Frontend Calculations**: All salary calculations performed on frontend using user's percentage
- **Permission-based Access**: Role-based access control for viewing salary data

## Access Control

- **Dentists**: Can view their own salary data
- **Admin/Master**: Can view salary data for any dentist in the clinic
- **Other roles**: No access to salary feature

## How to Use

### Accessing Salary Data

1. Navigate to the **Users** page
2. Find the dentist whose salary you want to view
3. Click the **wallet icon** (💰) next to their name in the actions column
4. The salary dialog will open for that specific user

### Using the Salary Dialog

1. The dialog opens with the selected user's name in the title
2. Choose the date range using the date pickers (defaults to current month)
3. Click "Apply" to fetch salary data
4. View the summary cards showing:
   - **Total Revenue**: Sum of all procedure net amounts (price - discount)
   - **Percentage**: The user's salary percentage from their profile
   - **Salary**: Calculated salary (Total Revenue × Percentage ÷ 100)
5. Review the procedures list for detailed breakdown

## Technical Implementation

### Models

- **DentistSalaryResponse**: Simplified response model containing only procedures array
- **SalaryProcedure**: Individual procedure data with patient information
- **PatientModel**: Full patient model used for procedure display

### API Endpoint

- **GET** `/users/:uid/salary?from=YYYY-MM-DD&to=YYYY-MM-DD`
- Requires authentication token
- Returns simplified response with only procedures array
- Frontend calculates totals, percentage, and salary using user's profile data

### State Management

- Uses GetX for reactive state management
- States: Initial, Loading, Loaded, Error
- Automatic refresh when parameters change

### UI Components

- **BasicDialog**: Reusable dialog component
- **Date Pickers**: Standard Flutter date selection
- **Summary Cards**: Color-coded cards for key metrics
- **Procedures Table**: Responsive table with patient and procedure details

## Error Handling

- **Permission Errors**: Clear messages for unauthorized access
- **Validation Errors**: Date range validation (from ≤ to)
- **Network Errors**: Retry functionality with error display
- **Empty States**: Friendly messages when no data is available

## Testing

The feature includes comprehensive unit tests covering:

- Model serialization/deserialization
- Net amount calculations
- Edge cases (empty procedures, zero amounts)
- JSON parsing validation

Run tests with:

```bash
flutter test test/salary_feature_test.dart
```

## File Structure

````
lib/
├── core/
│   ├── api/
│   │   └── users.dart (added getDentistSalary method)
│   └── models/
│       ├── dentist_salary.dart (simplified - only procedures)
│       └── salary_procedure.dart (uses PatientModel)
├── pages/
│   ├── salary/
│   │   ├── controller.dart (accepts user parameter)
│   │   └── salary_dialog.dart (user-specific dialog)
│   └── users/
│       └── list/
│           └── list.dart (added salary button)

test/
└── salary_feature_test.dart

## Backend Response Format

The backend now returns a simplified response containing only the procedures array:

```json
{
  "procedures": [
    {
      "date": "2025-08-12",
      "patient": {
        "id": "string",
        "name": "string",
        "phoneNumber": "string",
        "fileNumber": "string",
        // ... other patient fields
      },
      "procedure": "string",
      "speciality": "string",
      "toothNumber": "string",
      "price": 500,
      "discount": 50,
      "notes": "string",
      "visitId": "string",
      "dentistId": "string"
    }
  ]
}
````

The frontend calculates:

- **Total Revenue**: Sum of (price - discount) for all procedures
- **Percentage**: Retrieved from user.percentage field
- **Salary**: Total Revenue × (Percentage ÷ 100)

```

## Future Enhancements

- Export salary data to PDF/Excel
- Historical salary comparisons
- Salary trends and analytics
- Email salary reports
- Bulk salary calculations for multiple dentists
- Integration with payroll systems

## Dependencies

- GetX: State management
- Jiffy: Date formatting
- JSON Annotation: Model serialization
- Flutter Material: UI components

## Notes

- The feature follows existing codebase patterns and conventions
- All text is hardcoded in English (can be internationalized later)
- Currency formatting uses the clinic's configured currency
- Date format follows YYYY-MM-DD pattern for API consistency
```
