import 'package:flutter_test/flutter_test.dart';
import 'package:core_app/core/models/dentist_salary.dart';
import 'package:core_app/core/models/salary_procedure.dart';
import 'package:core_app/core/models/patient.dart';

void main() {
  group('Dentist Salary Models', () {
    // Helper method to create a test patient
    PatientModel createTestPatient() {
      return PatientModel(
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        address: '123 Main St',
        birthdate: DateTime(1990, 1, 1),
        balance: 0.0,
        job: 'Engineer',
        dentalHistory: '',
        medicalHistory: '',
        treatmentPlan: '',
        fileNumber: 'F001',
        reachChannel: 'phone',
        createdById: 'creator1',
        createdAt: DateTime.now(),
        teethStatus: {},
        customReachChannel: null,
        patientGroups: [],
      );
    }

    test('SalaryProcedure model should calculate net amount correctly', () {
      final patient = createTestPatient();

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      expect(procedure.netAmount, equals(450.0));
    });

    test('SalaryProcedure model properties work correctly', () {
      final patient = createTestPatient();

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      expect(procedure.date, equals('2025-08-12'));
      expect(procedure.patient.name, equals('John Doe'));
      expect(procedure.procedure, equals('Root Canal'));
      expect(procedure.speciality, equals('Endodontics'));
      expect(procedure.toothNumber, equals('14'));
      expect(procedure.price, equals(500.0));
      expect(procedure.discount, equals(50.0));
      expect(procedure.notes, equals('Test procedure'));
      expect(procedure.visitId, equals('visit1'));
      expect(procedure.dentistId, equals('dentist1'));
      expect(procedure.netAmount, equals(450.0));
    });

    test('DentistSalaryResponse model properties work correctly', () {
      final patient = createTestPatient();

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      final salaryResponse = DentistSalaryResponse(
        procedures: [procedure],
      );

      expect(salaryResponse.procedures.length, equals(1));
      expect(salaryResponse.proceduresCount, equals(1));
      expect(salaryResponse.totalRevenue, equals(450.0));
      expect(salaryResponse.calculateSalary(60.0), equals(270.0)); // 450 * 60%
    });

    test('DentistSalaryResponse should handle empty procedures list', () {
      final salaryResponse = DentistSalaryResponse(
        procedures: [],
      );

      expect(salaryResponse.proceduresCount, equals(0));
      expect(salaryResponse.totalRevenue, equals(0.0));
      expect(salaryResponse.calculateSalary(60.0), equals(0.0));
    });

    test('DentistSalaryResponse should calculate total revenue correctly', () {
      final patient = createTestPatient();

      final procedure1 = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure 1',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      final procedure2 = SalaryProcedure(
        date: '2025-08-13',
        patient: patient,
        procedure: 'Filling',
        speciality: 'General',
        toothNumber: '15',
        price: 200.0,
        discount: 20.0,
        notes: 'Test procedure 2',
        visitId: 'visit2',
        dentistId: 'dentist1',
      );

      final salaryResponse = DentistSalaryResponse(
        procedures: [procedure1, procedure2],
      );

      expect(salaryResponse.proceduresCount, equals(2));
      expect(salaryResponse.totalRevenue, equals(630.0)); // 450 + 180
      expect(salaryResponse.calculateSalary(60.0), equals(378.0)); // 630 * 60%
    });

    test('DentistSalaryResponse should calculate salary with different percentages', () {
      final patient = createTestPatient();

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 1000.0,
        discount: 0.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      final salaryResponse = DentistSalaryResponse(
        procedures: [procedure],
      );

      expect(salaryResponse.calculateSalary(50.0), equals(500.0)); // 1000 * 50%
      expect(salaryResponse.calculateSalary(60.0), equals(600.0)); // 1000 * 60%
      expect(salaryResponse.calculateSalary(70.0), equals(700.0)); // 1000 * 70%
      expect(salaryResponse.calculateSalary(0.0), equals(0.0)); // 1000 * 0%
    });
  });
}
