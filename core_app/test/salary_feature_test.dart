import 'package:flutter_test/flutter_test.dart';
import 'package:core_app/core/models/dentist_salary.dart';
import 'package:core_app/core/models/salary_procedure.dart';
import 'package:core_app/core/models/patient_lite.dart';

void main() {
  group('Dentist Salary Models', () {
    test('PatientLite model should serialize/deserialize correctly', () {
      final patient = PatientLite(
        id: '1',
        name: '<PERSON>',
        phoneNumber: '+**********',
        fileNumber: 'F001',
      );

      final json = patient.toJson();
      final fromJson = PatientLite.fromJson(json);

      expect(fromJson.id, equals(patient.id));
      expect(fromJson.name, equals(patient.name));
      expect(fromJson.phoneNumber, equals(patient.phoneNumber));
      expect(fromJson.fileNumber, equals(patient.fileNumber));
    });

    test('SalaryProcedure model should calculate net amount correctly', () {
      final patient = PatientLite(
        id: '1',
        name: '<PERSON>',
        phoneNumber: '+**********',
        fileNumber: 'F001',
      );

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      expect(procedure.netAmount, equals(450.0));
    });

    test('SalaryProcedure model should serialize/deserialize correctly', () {
      final patient = PatientLite(
        id: '1',
        name: 'John Doe',
        phoneNumber: '+**********',
        fileNumber: 'F001',
      );

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      final json = procedure.toJson();
      // Convert patient object to JSON for proper serialization test
      json['patient'] = patient.toJson();
      final fromJson = SalaryProcedure.fromJson(json);

      expect(fromJson.date, equals(procedure.date));
      expect(fromJson.patient.name, equals(procedure.patient.name));
      expect(fromJson.procedure, equals(procedure.procedure));
      expect(fromJson.speciality, equals(procedure.speciality));
      expect(fromJson.toothNumber, equals(procedure.toothNumber));
      expect(fromJson.price, equals(procedure.price));
      expect(fromJson.discount, equals(procedure.discount));
      expect(fromJson.notes, equals(procedure.notes));
      expect(fromJson.visitId, equals(procedure.visitId));
      expect(fromJson.dentistId, equals(procedure.dentistId));
    });

    test('DentistSalaryResponse model should serialize/deserialize correctly',
        () {
      final patient = PatientLite(
        id: '1',
        name: 'John Doe',
        phoneNumber: '+**********',
        fileNumber: 'F001',
      );

      final procedure = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      final salaryResponse = DentistSalaryResponse(
        total: 1234.5,
        percentage: 60.0,
        salary: 740.7,
        from: '2025-08-01',
        to: '2025-08-31',
        procedures: [procedure],
      );

      final json = salaryResponse.toJson();
      // Convert procedures list to proper JSON format
      json['procedures'] = [procedure.toJson()..['patient'] = patient.toJson()];
      final fromJson = DentistSalaryResponse.fromJson(json);

      expect(fromJson.total, equals(salaryResponse.total));
      expect(fromJson.percentage, equals(salaryResponse.percentage));
      expect(fromJson.salary, equals(salaryResponse.salary));
      expect(fromJson.from, equals(salaryResponse.from));
      expect(fromJson.to, equals(salaryResponse.to));
      expect(fromJson.procedures.length, equals(1));
      expect(fromJson.proceduresCount, equals(1));
      expect(fromJson.totalNetAmount, equals(450.0));
    });

    test('DentistSalaryResponse should handle empty procedures list', () {
      final salaryResponse = DentistSalaryResponse(
        total: 0.0,
        percentage: 60.0,
        salary: 0.0,
        from: '2025-08-01',
        to: '2025-08-31',
        procedures: [],
      );

      expect(salaryResponse.proceduresCount, equals(0));
      expect(salaryResponse.totalNetAmount, equals(0.0));
    });

    test('DentistSalaryResponse should calculate total net amount correctly',
        () {
      final patient = PatientLite(
        id: '1',
        name: 'John Doe',
        phoneNumber: '+**********',
        fileNumber: 'F001',
      );

      final procedure1 = SalaryProcedure(
        date: '2025-08-12',
        patient: patient,
        procedure: 'Root Canal',
        speciality: 'Endodontics',
        toothNumber: '14',
        price: 500.0,
        discount: 50.0,
        notes: 'Test procedure 1',
        visitId: 'visit1',
        dentistId: 'dentist1',
      );

      final procedure2 = SalaryProcedure(
        date: '2025-08-13',
        patient: patient,
        procedure: 'Filling',
        speciality: 'General',
        toothNumber: '15',
        price: 200.0,
        discount: 20.0,
        notes: 'Test procedure 2',
        visitId: 'visit2',
        dentistId: 'dentist1',
      );

      final salaryResponse = DentistSalaryResponse(
        total: 700.0,
        percentage: 60.0,
        salary: 420.0,
        from: '2025-08-01',
        to: '2025-08-31',
        procedures: [procedure1, procedure2],
      );

      expect(salaryResponse.proceduresCount, equals(2));
      expect(salaryResponse.totalNetAmount, equals(630.0)); // 450 + 180
    });
  });
}
