import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/dentist_salary.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/services/auth.dart';
import 'package:get/get.dart';

enum SalaryState { initial, loading, loaded, error }

class DentistSalaryController extends GetxController {
  static DentistSalaryController get to => Get.find();

  final UserModel user;

  // State management
  final state = SalaryState.initial.obs;
  final errorMessage = ''.obs;
  final salaryData = Rx<DentistSalaryResponse?>(null);

  // Filter parameters
  final fromDate = Rx<DateTime?>(null);
  final toDate = Rx<DateTime?>(null);

  // Last used parameters for refresh functionality
  DateTime? _lastFromDate;
  DateTime? _lastToDate;

  DentistSalaryController({required this.user});

  @override
  void onInit() {
    super.onInit();
    _initializeDates();
  }

  /// Initialize default date range (current month)
  void _initializeDates() {
    final now = DateTime.now();
    fromDate.value = DateTime(now.year, now.month, 1);
    toDate.value = DateTime(now.year, now.month + 1, 0);
  }

  /// Check if current user can access salary data for the user
  bool get canAccessSalary {
    final currentUser = AuthService.getUser;

    // Self access
    if (user.id == currentUser.id) return true;

    // Master and admin can access any dentist's salary
    return currentUser.role == UserRole.master ||
        currentUser.role == UserRole.admin;
  }

  /// Load salary data with current parameters
  Future<void> load() async {
    final from = fromDate.value;
    final to = toDate.value;

    if (from == null || to == null) {
      errorMessage.value = 'Please select a date range';
      state.value = SalaryState.error;
      return;
    }

    if (!canAccessSalary) {
      errorMessage.value =
          'You do not have permission to view this salary data';
      state.value = SalaryState.error;
      return;
    }

    if (from.isAfter(to)) {
      errorMessage.value = 'From date must be before or equal to To date';
      state.value = SalaryState.error;
      return;
    }

    await _fetchSalaryData(user.id, from, to);
  }

  /// Refresh with last used parameters
  @override
  Future<void> refresh() async {
    if (_lastFromDate != null && _lastToDate != null) {
      fromDate.value = _lastFromDate;
      toDate.value = _lastToDate;
      await _fetchSalaryData(user.id, _lastFromDate!, _lastToDate!);
    }
  }

  /// Internal method to fetch salary data
  Future<void> _fetchSalaryData(
      String userId, DateTime from, DateTime to) async {
    state.value = SalaryState.loading;
    errorMessage.value = '';

    try {
      final response = await UsersAPI.getDentistSalary(
        userId: userId,
        from: from,
        to: to,
      );

      salaryData.value = response;
      state.value = SalaryState.loaded;

      // Store parameters for refresh
      _lastFromDate = from;
      _lastToDate = to;
    } catch (e) {
      errorMessage.value = e.toString();
      state.value = SalaryState.error;
      salaryData.value = null;
    }
  }

  /// Update date range and automatically refetch if data was previously loaded
  void updateDateRange(DateTime from, DateTime to) {
    fromDate.value = from;
    toDate.value = to;

    if (state.value == SalaryState.loaded) {
      load();
    }
  }
}
