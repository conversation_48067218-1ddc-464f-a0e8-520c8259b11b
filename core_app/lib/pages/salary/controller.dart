import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/dentist_salary.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/services/auth.dart';
import 'package:get/get.dart';

enum SalaryState { initial, loading, loaded, error }

class DentistSalaryController extends GetxController {
  static DentistSalaryController get to => Get.find();

  // State management
  final state = SalaryState.initial.obs;
  final errorMessage = ''.obs;
  final salaryData = Rx<DentistSalaryResponse?>(null);

  // Filter parameters
  final selectedUserId = Rx<String?>(null);
  final fromDate = Rx<DateTime?>(null);
  final toDate = Rx<DateTime?>(null);
  final allDentists = <UserModel>[].obs;

  // Last used parameters for refresh functionality
  String? _lastUserId;
  DateTime? _lastFromDate;
  DateTime? _lastToDate;

  @override
  void onInit() {
    super.onInit();
    _initializeDates();
    _loadDentists();
  }

  /// Initialize default date range (current month)
  void _initializeDates() {
    final now = DateTime.now();
    fromDate.value = DateTime(now.year, now.month, 1);
    toDate.value = DateTime(now.year, now.month + 1, 0);
  }

  /// Load available dentists for selection
  void _loadDentists() {
    tryAPI(() async {
      final users = await UsersAPI.list();
      allDentists.value = users.where((user) => user.isDentist).toList();

      // Auto-select current user if they are a dentist
      final currentUser = AuthService.getUser;
      if (currentUser.isDentist) {
        selectedUserId.value = currentUser.id;
      } else if (allDentists.length == 1) {
        // Auto-select if only one dentist available
        selectedUserId.value = allDentists.first.id;
      }
    });
  }

  /// Check if current user can access salary data for the selected user
  bool get canAccessSalary {
    final currentUser = AuthService.getUser;
    final selectedId = selectedUserId.value;

    if (selectedId == null) return false;

    // Self access
    if (selectedId == currentUser.id) return true;

    // Master and admin can access any dentist's salary
    return currentUser.role == UserRole.master ||
        currentUser.role == UserRole.admin;
  }

  /// Load salary data with current parameters
  Future<void> load() async {
    final userId = selectedUserId.value;
    final from = fromDate.value;
    final to = toDate.value;

    if (userId == null || from == null || to == null) {
      errorMessage.value = 'Please select a dentist and date range';
      state.value = SalaryState.error;
      return;
    }

    if (!canAccessSalary) {
      errorMessage.value =
          'You do not have permission to view this salary data';
      state.value = SalaryState.error;
      return;
    }

    if (from.isAfter(to)) {
      errorMessage.value = 'From date must be before or equal to To date';
      state.value = SalaryState.error;
      return;
    }

    await _fetchSalaryData(userId, from, to);
  }

  /// Refresh with last used parameters
  @override
  Future<void> refresh() async {
    if (_lastUserId != null && _lastFromDate != null && _lastToDate != null) {
      selectedUserId.value = _lastUserId;
      fromDate.value = _lastFromDate;
      toDate.value = _lastToDate;
      await _fetchSalaryData(_lastUserId!, _lastFromDate!, _lastToDate!);
    }
  }

  /// Internal method to fetch salary data
  Future<void> _fetchSalaryData(
      String userId, DateTime from, DateTime to) async {
    state.value = SalaryState.loading;
    errorMessage.value = '';

    try {
      final response = await UsersAPI.getDentistSalary(
        userId: userId,
        from: from,
        to: to,
      );

      salaryData.value = response;
      state.value = SalaryState.loaded;

      // Store parameters for refresh
      _lastUserId = userId;
      _lastFromDate = from;
      _lastToDate = to;
    } catch (e) {
      errorMessage.value = e.toString();
      state.value = SalaryState.error;
      salaryData.value = null;
    }
  }

  /// Update date range and automatically refetch if data was previously loaded
  void updateDateRange(DateTime from, DateTime to) {
    fromDate.value = from;
    toDate.value = to;

    if (state.value == SalaryState.loaded) {
      load();
    }
  }

  /// Update selected dentist and automatically refetch if data was previously loaded
  void updateSelectedDentist(String? userId) {
    selectedUserId.value = userId;

    if (state.value == SalaryState.loaded && userId != null) {
      load();
    }
  }

  /// Get selected dentist model
  UserModel? get selectedDentist {
    final userId = selectedUserId.value;
    if (userId == null) return null;

    try {
      return allDentists.firstWhere((dentist) => dentist.id == userId);
    } catch (e) {
      return null;
    }
  }

  /// Check if user can select dentists (master/admin only)
  bool get canSelectDentist {
    final currentUser = AuthService.getUser;
    return currentUser.role == UserRole.master ||
        currentUser.role == UserRole.admin;
  }
}
