import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/users/form/form.dart';
import 'package:core_app/pages/users/list/controller.dart';
import 'package:core_app/pages/users/timesheet/user_timesheet_dialog.dart';
import 'package:core_app/pages/salary/salary_dialog.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/routes/route_paths.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UsersListPage extends StatelessWidget {
  const UsersListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<UsersListController>();
    return PageLayout(
      children: [
        Obx(
          () => TableComponent<UserModel>(
            title: t.users.list,
            data: c.users.value,
            actions: [
              XTextIconButton(
                title: t.users.add,
                icon: Iconsax.add_copy,
                onPressed: () async {
                  if (AuthService.to.isSubEnded) {
                    NotifyService.notice(title: t.subEnded);
                    return;
                  }
                  UserModel? wait = await Get.dialog(const UserFormDialog());
                  if (wait != null) {
                    await c.fetch();
                    return;
                  }
                  NotifyService.success(t.users.userAdded);
                },
              ),
              XTextIconButton(
                title: "Timesheet",
                icon: Iconsax.calendar_copy,
                onPressed: () async {
                  Navigation.to(Routes.Timesheet);
                },
              ),
            ],
            columns: [
              TableColumn(
                flex: 2,
                title: t.userForm.name,
                minWidth: 200,
                builder: (data) {
                  return TableColumn.stringBuilder(data.name);
                },
              ),
              TableColumn(
                flex: 2,
                title: t.userForm.role,
                minWidth: 100,
                builder: (data) {
                  return TableColumn.stringBuilder(
                      data.role.name.capitalizeFirst!);
                },
              ),
              // if (AuthService.getUser.role == UserRole.master)
              //   TableColumn(
              //     flex: 2,
              //     title: 'percentage'.tr,
              //     minWidth: 50,
              //     builder: (data) {
              //       if (data.percentage == null) return TableColumn.stringBuilder('N/A');
              //       return TableColumn.stringBuilder('${data.percentage}%');
              //     },
              //   ),
              TableColumn(
                flex: 2,
                title: t.userForm.isDentist,
                minWidth: 100,
                builder: (data) {
                  return data.isDentist
                      ? const Align(
                          alignment: Alignment.centerLeft,
                          child: Icon(
                            Icons.check_rounded,
                            color: ThemeColors.text,
                          ),
                        )
                      : const SizedBox();
                },
              ),
              if (c.master)
                TableColumn(
                  title: '',
                  minWidth: 150,
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (AuthService.to.user.value!.role == UserRole.master)
                          if (data.hourlyRate != null && data.hourlyRate! > 0)
                            IconButton(
                              iconSize: 18,
                              icon: const Icon(
                                Iconsax.calendar_copy,
                                size: 18,
                              ),
                              tooltip: 'View Timesheet',
                              onPressed: () async {
                                if (AuthService.to.isSubEnded) {
                                  NotifyService.notice(title: t.subEnded);
                                  return;
                                }
                                await Get.dialog(
                                    UserTimesheetDialog(user: data));
                              },
                            ),
                        // Salary button - only show for dentists
                        if (AuthService.to.user.value!.role == UserRole.master)
                          if (data.isDentist &&
                              data.percentage != null &&
                              data.percentage! > 0)
                            IconButton(
                              iconSize: 18,
                              icon: const Icon(
                                Iconsax.wallet_money_copy,
                                size: 18,
                              ),
                              tooltip: 'View Salary',
                              onPressed: () async {
                                if (AuthService.to.isSubEnded) {
                                  NotifyService.notice(title: t.subEnded);
                                  return;
                                }
                                await Get.dialog(
                                    DentistSalaryDialog(user: data));
                              },
                            ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            UserModel? user =
                                await Get.dialog(UserFormDialog(user: data));
                            if (user != null) {
                              var indexWhere = c.users.indexWhere(
                                  (element) => element.id == data.id);
                              c.users[indexWhere] = user;
                            }
                          },
                        ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.trash_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            final confirm =
                                await Get.dialog(const ConfirmUserDelete());
                            if (confirm == true) {
                              tryAPI(() async {
                                await UsersAPI.delete(data.id);
                                c.users.remove(data);
                                await c.fetch();
                                NotifyService.success(t.users.userDeleted);
                              });
                            }
                          },
                        ),
                      ],
                    );
                  },
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class ConfirmUserDelete extends StatelessWidget {
  const ConfirmUserDelete({super.key});

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      title: t.users.confirmDelete,
      children: [
        Text(t.users.confirmDeleteUser),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor: ThemeColors.error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                Get.back(result: false);
              },
              child: Text(
                t.cancel,
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 16),
            TextButton(
              onPressed: () {
                Get.back(result: true);
              },
              child: Text(
                t.confirm,
                style: const TextStyle(
                  color: ThemeColors.text,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
