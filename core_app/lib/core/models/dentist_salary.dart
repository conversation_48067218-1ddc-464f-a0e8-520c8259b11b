import 'package:core_app/core/models/salary_procedure.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/dentist_salary.g.dart';

@JsonSerializable()
class DentistSalaryResponse {
  final double total;
  final double percentage;
  final double salary;
  final String from;
  final String to;
  @JsonKey(defaultValue: [])
  final List<SalaryProcedure> procedures;

  const DentistSalaryResponse({
    required this.total,
    required this.percentage,
    required this.salary,
    required this.from,
    required this.to,
    required this.procedures,
  });

  factory DentistSalaryResponse.fromJson(Map<String, dynamic> json) =>
      _$DentistSalaryResponseFromJson(json);
  Map<String, dynamic> toJson() => _$DentistSalaryResponseToJson(this);

  /// Get total number of procedures
  int get proceduresCount => procedures.length;

  /// Get total net amount from all procedures
  double get totalNetAmount =>
      procedures.fold(0.0, (sum, proc) => sum + proc.netAmount);
}
