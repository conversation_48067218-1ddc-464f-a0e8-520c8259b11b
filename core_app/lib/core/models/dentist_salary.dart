import 'package:core_app/core/models/salary_procedure.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/dentist_salary.g.dart';

@JsonSerializable()
class DentistSalaryResponse {
  @JsonKey(defaultValue: [])
  final List<SalaryProcedure> procedures;

  const DentistSalaryResponse({
    required this.procedures,
  });

  factory DentistSalaryResponse.fromJson(Map<String, dynamic> json) =>
      _$DentistSalaryResponseFromJson(json);
  Map<String, dynamic> toJson() => _$DentistSalaryResponseToJson(this);

  /// Get total number of procedures
  int get proceduresCount => procedures.length;

  /// Get total revenue from all procedures (price - discount)
  double get totalRevenue =>
      procedures.fold(0.0, (sum, proc) => sum + proc.netAmount);

  /// Calculate salary based on user's percentage
  double calculateSalary(double userPercentage) {
    return totalRevenue * (userPercentage / 100);
  }
}
