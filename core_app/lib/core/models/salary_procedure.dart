import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/salary_procedure.g.dart';

@JsonSerializable()
class SalaryProcedure {
  final String date;
  final PatientModel patient;
  final String procedure;
  final String speciality;
  final String toothNumber;
  final double price;
  final double discount;
  @JsonString
  final String? notes;
  final String visitId;
  final String dentistId;

  const SalaryProcedure({
    required this.date,
    required this.patient,
    required this.procedure,
    required this.speciality,
    required this.toothNumber,
    required this.price,
    required this.discount,
    this.notes,
    required this.visitId,
    required this.dentistId,
  });

  factory SalaryProcedure.fromJson(Map<String, dynamic> json) =>
      _$SalaryProcedureFromJson(json);
  Map<String, dynamic> toJson() => _$SalaryProcedureToJson(this);

  /// Calculate net amount (price - discount)
  double get netAmount => price - discount;
}
