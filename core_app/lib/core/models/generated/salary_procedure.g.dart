// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../salary_procedure.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SalaryProcedure _$SalaryProcedureFromJson(Map<String, dynamic> json) =>
    SalaryProcedure(
      date: json['date'] as String,
      patient: PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      procedure: json['procedure'] as String,
      speciality: json['speciality'] as String,
      toothNumber: json['toothNumber'] as String,
      price: (json['price'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      notes: stringFromJson(json['notes'] as Map<String, dynamic>?),
      visitId: json['visitId'] as String,
      dentistId: json['dentistId'] as String,
    );

Map<String, dynamic> _$SalaryProcedureToJson(SalaryProcedure instance) =>
    <String, dynamic>{
      'date': instance.date,
      'patient': instance.patient,
      'procedure': instance.procedure,
      'speciality': instance.speciality,
      'toothNumber': instance.toothNumber,
      'price': instance.price,
      'discount': instance.discount,
      'notes': stringToJson(instance.notes),
      'visitId': instance.visitId,
      'dentistId': instance.dentistId,
    };
