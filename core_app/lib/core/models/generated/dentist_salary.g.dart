// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../dentist_salary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DentistSalaryResponse _$DentistSalaryResponseFromJson(
        Map<String, dynamic> json) =>
    DentistSalaryResponse(
      total: (json['total'] as num).toDouble(),
      percentage: (json['percentage'] as num).toDouble(),
      salary: (json['salary'] as num).toDouble(),
      from: json['from'] as String,
      to: json['to'] as String,
      procedures: (json['procedures'] as List<dynamic>?)
              ?.map((e) => SalaryProcedure.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$DentistSalaryResponseToJson(
        DentistSalaryResponse instance) =>
    <String, dynamic>{
      'total': instance.total,
      'percentage': instance.percentage,
      'salary': instance.salary,
      'from': instance.from,
      'to': instance.to,
      'procedures': instance.procedures,
    };
