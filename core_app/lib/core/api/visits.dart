import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/visit.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:core_app/services/api.dart';

class VisitsAPI {
  /// limit per page = 20
  static Future<List<VisitModel>> list({
    int page = 1,
    int perPage = 20,
    String? patientId,
    String? dentistId,
  }) async {
    final DioRes response = await ApiService.get(
      '/visits',
      queryParameters: {
        "page": page,
        "count": perPage,
        if (patientId != null) "patientId": patientId,
        if (dentistId != null) "dentistId": dentistId,
      },
    );
    return (response.data!['visits'] as List)
        .map<VisitModel>((e) => VisitModel.fromJson(e))
        .toList();
  }

  static Future<List<VisitProcedureModel>> listProcedures(String id) async {
    final DioRes response = await ApiService.get('/visits/$id/procedures');
    return (response.data!['procedures'] as List<Json>)
        .map<VisitProcedureModel>((e) => VisitProcedureModel.fromJson(e))
        .toList();
  }

  static Future<VisitModel> create({
    required String branchId,
    required String patientId,
    required String diagnosis,
    required String comments,
    required String nextVisit,
    required List<String> treatments,
    required String appointmentId,
  }) async {
    final DioRes response = await ApiService.post('/visits', data: {
      "branchId": branchId,
      "patientId": patientId,
      "diagnosis": diagnosis,
      "comments": comments,
      "treatments": treatments,
      "appointmentId": appointmentId,
      "nextVisit": nextVisit,
    });
    return VisitModel.fromJson(response.data!['visit']);
  }
}
